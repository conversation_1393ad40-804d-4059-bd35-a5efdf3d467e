-- Seed default users with encrypted passwords
-- This migration creates default admin and sub-admin users for the system

-- First, let's create a function to create users with encrypted passwords
-- Note: For local development, we'll use a simple hash. In production, use Supabase Auth API
CREATE OR REPLACE FUNCTION create_default_user(
  user_email TEXT,
  user_password TEXT,
  user_name TEXT,
  user_role TEXT
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = auth, public
AS $$
DECLARE
  new_user_id UUID;
  encrypted_password TEXT;
BEGIN
  -- Generate a UUID for the user
  new_user_id := gen_random_uuid();

  -- For local development, we'll use a simple approach
  -- In production, use Supabase Auth API which handles encryption properly
  -- This is just for seeding local development data
  encrypted_password := user_password;
  
  -- Insert into auth.users table
  INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    aud,
    role,
    confirmation_token,
    email_change_token_new,
    recovery_token,
    raw_user_meta_data
  ) VALUES (
    new_user_id,
    '00000000-0000-0000-0000-000000000000',
    user_email,
    encrypted_password,
    now(),
    now(),
    now(),
    'authenticated',
    'authenticated',
    '',
    '',
    '',
    jsonb_build_object('name', user_name)
  );
  
  -- Insert into auth.identities table
  INSERT INTO auth.identities (
    id,
    user_id,
    identity_data,
    provider,
    provider_id,
    created_at,
    updated_at
  ) VALUES (
    gen_random_uuid(),
    new_user_id,
    jsonb_build_object(
      'sub', new_user_id::text,
      'email', user_email,
      'name', user_name
    ),
    'email',
    user_email,
    now(),
    now()
  );
  
  -- The profile will be created automatically by the trigger
  -- But we need to update the role after creation
  -- Wait a moment for the trigger to execute, then update the role
  PERFORM pg_sleep(0.1);

  UPDATE public.profiles
  SET role = user_role
  WHERE public.profiles.user_id = new_user_id;

  RETURN new_user_id;
END;
$$;

-- Create default admin user
SELECT create_default_user(
  '<EMAIL>',
  'admin123!',
  'System Administrator',
  'admin'
);

-- Create default sub-admin user
SELECT create_default_user(
  '<EMAIL>',
  'subadmin123!',
  'Sub Administrator',
  'sub-admin'
);

-- Create a few more example users for testing
SELECT create_default_user(
  '<EMAIL>',
  'manager123!',
  'Community Manager',
  'sub-admin'
);

SELECT create_default_user(
  '<EMAIL>',
  'moderator123!',
  'Community Moderator',
  'sub-admin'
);

-- Drop the helper function as it's no longer needed
DROP FUNCTION create_default_user(TEXT, TEXT, TEXT, TEXT);

-- Add a comment to document the default credentials
COMMENT ON TABLE public.profiles IS 'Default users created:
- <EMAIL> / admin123! (admin)
- <EMAIL> / subadmin123! (sub-admin)
- <EMAIL> / manager123! (sub-admin)
- <EMAIL> / moderator123! (sub-admin)

These are default credentials for initial setup. Change passwords after first login.';
