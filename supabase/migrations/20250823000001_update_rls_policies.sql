-- Update RLS policies to ensure only admins can create new users
-- This migration modifies the existing policies to be more restrictive

-- Ensure pgcrypto extension is available for password hashing
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Drop existing profile policies
DROP POLICY IF EXISTS "Users can create their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can delete their own profile" ON public.profiles;

-- Create new policies with admin restrictions

-- Allow profile creation only by the system (for new auth users) or by admins
CREATE POLICY "System can create profiles for new users" 
ON public.profiles 
FOR INSERT 
WITH CHECK (
  -- Allow if it's the user creating their own profile (from trigger)
  auth.uid() = user_id
  OR
  -- Allow if the current user is an admin
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin'
  )
);

-- Allow users to update their own profile, but only admins can change roles
CREATE POLICY "Users can update their own profile" 
ON public.profiles 
FOR UPDATE 
USING (
  auth.uid() = user_id
  OR
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin'
  )
)
WITH CHECK (
  -- Admins can update any profile including roles
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
  OR
  -- Users can update their own profile (role changes will be restricted by application logic)
  auth.uid() = user_id
);

-- Only admins can delete profiles
CREATE POLICY "Only admins can delete profiles" 
ON public.profiles 
FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin'
  )
);

-- Create a function that admins can use to create new users
CREATE OR REPLACE FUNCTION public.create_user_as_admin(
  user_email TEXT,
  user_password TEXT,
  user_name TEXT,
  user_role TEXT DEFAULT 'sub-admin'
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = auth, public
AS $$
DECLARE
  new_user_id UUID;
  encrypted_password TEXT;
  current_user_role TEXT;
BEGIN
  -- Check if the current user is an admin
  SELECT role INTO current_user_role
  FROM public.profiles
  WHERE user_id = auth.uid();
  
  IF current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Only administrators can create new users';
  END IF;
  
  -- Validate role
  IF user_role NOT IN ('admin', 'sub-admin') THEN
    RAISE EXCEPTION 'Invalid role. Must be admin or sub-admin';
  END IF;
  
  -- Generate a UUID for the user
  new_user_id := gen_random_uuid();
  
  -- For local development, we'll use a simple approach
  -- In production, use Supabase Auth API which handles encryption properly
  -- This is just for seeding local development data
  encrypted_password := user_password;
  
  -- Insert into auth.users table
  INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    aud,
    role,
    confirmation_token,
    email_change_token_new,
    recovery_token,
    raw_user_meta_data
  ) VALUES (
    new_user_id,
    '00000000-0000-0000-0000-000000000000',
    user_email,
    encrypted_password,
    now(),
    now(),
    now(),
    'authenticated',
    'authenticated',
    '',
    '',
    '',
    jsonb_build_object('name', user_name)
  );
  
  -- Insert into auth.identities table
  INSERT INTO auth.identities (
    id,
    user_id,
    identity_data,
    provider,
    provider_id,
    created_at,
    updated_at
  ) VALUES (
    gen_random_uuid(),
    new_user_id,
    jsonb_build_object(
      'sub', new_user_id::text,
      'email', user_email,
      'name', user_name
    ),
    'email',
    user_email,
    now(),
    now()
  );
  
  -- Create the profile directly with the correct role
  INSERT INTO public.profiles (user_id, name, email, role)
  VALUES (new_user_id, user_name, user_email, user_role);

  RETURN new_user_id;
END;
$$;

-- Grant execute permission to authenticated users (the function itself checks for admin role)
GRANT EXECUTE ON FUNCTION public.create_user_as_admin(TEXT, TEXT, TEXT, TEXT) TO authenticated;
