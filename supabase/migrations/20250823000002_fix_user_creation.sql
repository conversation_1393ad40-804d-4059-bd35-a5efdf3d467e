-- Fix user creation by updating existing users with proper password hashes
-- This migration fixes the password hashing for existing users

-- Enable pgcrypto extension for proper password hashing
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Update existing users with properly hashed passwords
UPDATE auth.users
SET encrypted_password = crypt('admin123!', gen_salt('bf', 10))
WHERE email = '<EMAIL>';

UPDATE auth.users
SET encrypted_password = crypt('subadmin123!', gen_salt('bf', 10))
WHERE email = '<EMAIL>';

UPDATE auth.users
SET encrypted_password = crypt('manager123!', gen_salt('bf', 10))
WHERE email = '<EMAIL>';

UPDATE auth.users
SET encrypted_password = crypt('moderator123!', gen_salt('bf', 10))
WHERE email = '<EMAIL>';
  
-- Default users for local development:
-- - <EMAIL> / admin123! (admin)
-- - <EMAIL> / subadmin123! (sub-admin)
-- - <EMAIL> / manager123! (sub-admin)
-- - <EMAIL> / moderator123! (sub-admin)
-- Passwords have been properly hashed using bcrypt and can be used for authentication.
