@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 220 25% 97%;
    --foreground: 220 15% 20%;

    --card: 0 0% 100%;
    --card-foreground: 220 15% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 15% 20%;

    --primary: 220 70% 25%;
    --primary-foreground: 220 25% 97%;
    --primary-glow: 220 60% 45%;

    --secondary: 220 15% 90%;
    --secondary-foreground: 220 15% 25%;

    --muted: 220 15% 94%;
    --muted-foreground: 220 10% 55%;

    --accent: 45 100% 65%;
    --accent-foreground: 220 15% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 15% 88%;
    --input: 220 15% 92%;
    --ring: 220 70% 25%;

    /* Custom design tokens */
    --gradient-primary: linear-gradient(135deg, hsl(220 70% 25%), hsl(260 60% 35%));
    --gradient-secondary: linear-gradient(135deg, hsl(220 15% 94%), hsl(220 25% 97%));
    --gradient-accent: linear-gradient(135deg, hsl(45 100% 65%), hsl(35 100% 60%));
    --shadow-elegant: 0 10px 30px -10px hsl(220 70% 25% / 0.2);
    --shadow-glow: 0 0 40px hsl(220 60% 45% / 0.15);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}