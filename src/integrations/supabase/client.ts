// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://bskaqpwdwpyplmqtypud.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJza2FxcHdkd3B5cGxtcXR5cHVkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUyNjQ4NjUsImV4cCI6MjA3MDg0MDg2NX0.6UCdbKubbUqEYOnkUKyA4yOgCXurn6p8pIuyQb62WQk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});