import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Camera, Users, Plus } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import AdminControls from "@/components/AdminControls";

const HallOfFame = () => {
  const { isAuthenticated, isAdmin, isSubAdmin } = useAuth();
  const { toast } = useToast();
  const [featured, setFeatured] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: "",
    achievement: "",
    image: "",
    type: "person"
  });

  const fetchHallOfFame = async () => {
    try {
      const { data, error } = await supabase
        .from('hall_of_fame')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setFeatured(data || []);
    } catch (error) {
      console.error('Error fetching hall of fame:', error);
      toast({
        title: "Error",
        description: "Failed to load hall of fame entries",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHallOfFame();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.achievement) return;

    try {
      if (isEditing && editingItem) {
        const { error } = await supabase
          .from('hall_of_fame')
          .update(formData)
          .eq('id', editingItem.id);
        
        if (error) throw error;
        toast({
          title: "Success",
          description: "Hall of fame entry updated successfully",
        });
      } else {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('Not authenticated');
        
        const { error } = await supabase
          .from('hall_of_fame')
          .insert([{ ...formData, user_id: user.id }]);
        
        if (error) throw error;
        toast({
          title: "Success",
          description: "Hall of fame entry created successfully",
        });
      }

      fetchHallOfFame();
      resetForm();
    } catch (error) {
      console.error('Error saving hall of fame entry:', error);
      toast({
        title: "Error",
        description: "Failed to save hall of fame entry",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (item: any) => {
    setEditingItem(item);
    setFormData({
      name: item.name,
      achievement: item.achievement,
      image: item.image || "",
      type: item.type
    });
    setIsEditing(true);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this entry?')) return;

    try {
      const { error } = await supabase
        .from('hall_of_fame')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Hall of fame entry deleted successfully",
      });
      fetchHallOfFame();
    } catch (error) {
      console.error('Error deleting hall of fame entry:', error);
      toast({
        title: "Error",
        description: "Failed to delete hall of fame entry",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      achievement: "",
      image: "",
      type: "person"
    });
    setIsDialogOpen(false);
    setIsEditing(false);
    setEditingItem(null);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading hall of fame...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div className="text-center flex-1">
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent">
              Hall of Fame
            </h1>
            <p className="text-lg text-muted-foreground">
              Celebrating our community's finest achievements and memorable moments
            </p>
          </div>
          
          {(isAdmin || isSubAdmin) && (
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => { setIsEditing(false); setFormData({ name: "", achievement: "", image: "", type: "person" }); }} className="bg-primary hover:bg-primary/90">
                  <Plus size={16} />
                  <span className="ml-2">Add Entry</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>{isEditing ? 'Edit' : 'Add'} Hall of Fame Entry</DialogTitle>
                  <DialogDescription>
                    {isEditing ? 'Update' : 'Create'} a hall of fame entry for achievements and memorable moments.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name/Title</Label>
                    <Input
                      id="name"
                      placeholder="Enter name or event title"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="achievement">Achievement</Label>
                    <Textarea
                      id="achievement"
                      placeholder="Describe the achievement"
                      value={formData.achievement}
                      onChange={(e) => setFormData({ ...formData, achievement: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="image">Image URL (optional)</Label>
                    <Input
                      id="image"
                      type="url"
                      placeholder="Enter image URL"
                      value={formData.image}
                      onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type">Type</Label>
                    <select
                      id="type"
                      value={formData.type}
                      onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    >
                      <option value="person">Person</option>
                      <option value="event">Event</option>
                    </select>
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button type="submit" className="flex-1">{isEditing ? 'Update' : 'Add'} Entry</Button>
                    <Button type="button" variant="outline" onClick={resetForm}>
                      Cancel
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          )}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {featured.map((item) => (
            <Card 
              key={item.id} 
              className="group overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-0 relative"
              style={{ boxShadow: 'var(--shadow-elegant)' }}
            >
              <div className="relative overflow-hidden">
                <img 
                  src={item.image || "/api/placeholder/300/400"} 
                  alt={item.name}
                  className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="absolute top-3 right-3">
                  <Badge 
                    variant="secondary" 
                    className="bg-white/20 text-white border-white/30 backdrop-blur-sm"
                  >
                    {item.type === "person" ? (
                      <Users size={12} className="mr-1" />
                    ) : (
                      <Camera size={12} className="mr-1" />
                    )}
                    {item.type === "person" ? "Member" : "Event"}
                  </Badge>
                </div>

                {(isAdmin || isSubAdmin) && (
                  <div className="absolute top-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <AdminControls
                      onEdit={() => handleEdit(item)}
                      onDelete={() => handleDelete(item.id)}
                      className="bg-white/20 backdrop-blur-sm rounded-lg p-1"
                    />
                  </div>
                )}
              </div>
              
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-2 group-hover:text-primary transition-colors">
                  {item.name}
                </h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {item.achievement}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-secondary to-muted">
            <Camera size={20} className="text-primary" />
            <span className="text-sm font-medium">More achievements added monthly</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HallOfFame;