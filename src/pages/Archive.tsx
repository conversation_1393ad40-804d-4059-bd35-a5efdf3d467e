import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Calendar, Download, Eye, Filter, Plus } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import AdminControls from "@/components/AdminControls";

const Archive = () => {
  const { isAuthenticated, isAdmin, isSubAdmin } = useAuth();
  const { toast } = useToast();
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [archiveItems, setArchiveItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    category: "events",
    image: ""
  });

  const fetchArchiveItems = async () => {
    try {
      const { data, error } = await supabase
        .from('archive_items')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setArchiveItems(data || []);
    } catch (error) {
      console.error('Error fetching archive items:', error);
      toast({
        title: "Error",
        description: "Failed to load archive items",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchArchiveItems();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title || !formData.description) return;

    try {
      if (isEditing && editingItem) {
        const { error } = await supabase
          .from('archive_items')
          .update(formData)
          .eq('id', editingItem.id);
        
        if (error) throw error;
        toast({
          title: "Success",
          description: "Archive item updated successfully",
        });
      } else {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('Not authenticated');
        
        const { error } = await supabase
          .from('archive_items')
          .insert([{ ...formData, user_id: user.id }]);
        
        if (error) throw error;
        toast({
          title: "Success",
          description: "Archive item created successfully",
        });
      }

      fetchArchiveItems();
      resetForm();
    } catch (error) {
      console.error('Error saving archive item:', error);
      toast({
        title: "Error",
        description: "Failed to save archive item",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (item: any) => {
    setEditingItem(item);
    setFormData({
      title: item.title,
      description: item.description,
      category: item.category,
      image: item.image || ""
    });
    setIsEditing(true);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this archive item?')) return;

    try {
      const { error } = await supabase
        .from('archive_items')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Archive item deleted successfully",
      });
      fetchArchiveItems();
    } catch (error) {
      console.error('Error deleting archive item:', error);
      toast({
        title: "Error",
        description: "Failed to delete archive item",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      category: "events",
      image: ""
    });
    setIsDialogOpen(false);
    setIsEditing(false);
    setEditingItem(null);
  };

  const categories = [
    { value: "all", label: "All Items" },
    { value: "events", label: "Events" },
    { value: "ceremonies", label: "Ceremonies" },
    { value: "volunteer", label: "Volunteer" },
    { value: "programs", label: "Programs" },
    { value: "initiatives", label: "Initiatives" },
    { value: "meetings", label: "Meetings" }
  ];

  const filteredItems = selectedCategory === "all" 
    ? archiveItems 
    : archiveItems.filter(item => item.category === selectedCategory);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading archive...</p>
        </div>
      </div>
    );
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      events: "bg-primary text-primary-foreground",
      ceremonies: "bg-accent text-accent-foreground", 
      volunteer: "bg-green-500 text-white",
      programs: "bg-blue-500 text-white",
      initiatives: "bg-purple-500 text-white",
      meetings: "bg-orange-500 text-white"
    };
    return colors[category as keyof typeof colors] || "bg-secondary text-secondary-foreground";
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div className="text-center flex-1">
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent">
              Community Archive
            </h1>
            <p className="text-lg text-muted-foreground">
              Preserving our community's history and memorable moments
            </p>
          </div>
          
          {(isAdmin || isSubAdmin) && (
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => { setIsEditing(false); setFormData({ title: "", description: "", category: "events", image: "" }); }} className="bg-primary hover:bg-primary/90">
                  <Plus size={16} />
                  <span className="ml-2">Add Item</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>{isEditing ? 'Edit' : 'Add'} Archive Item</DialogTitle>
                  <DialogDescription>
                    {isEditing ? 'Update' : 'Create'} an archive item to preserve community history.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      placeholder="Enter item title"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="Describe the archived item"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <select
                      id="category"
                      value={formData.category}
                      onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    >
                      <option value="events">Events</option>
                      <option value="ceremonies">Ceremonies</option>
                      <option value="volunteer">Volunteer</option>
                      <option value="programs">Programs</option>
                      <option value="initiatives">Initiatives</option>
                      <option value="meetings">Meetings</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="image">Image URL (optional)</Label>
                    <Input
                      id="image"
                      type="url"
                      placeholder="Enter image URL"
                      value={formData.image}
                      onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                    />
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button type="submit" className="flex-1">{isEditing ? 'Update' : 'Add'} Item</Button>
                    <Button type="button" variant="outline" onClick={resetForm}>
                      Cancel
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          )}
        </div>

        {/* Filter Section */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Filter size={20} className="text-primary" />
            <h3 className="text-lg font-semibold">Filter by Category</h3>
          </div>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category.value}
                variant={selectedCategory === category.value ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.value)}
                className="transition-all duration-200"
              >
                {category.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Archive Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => (
            <Card 
              key={item.id}
              className="group overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border-0 relative"
              style={{ boxShadow: 'var(--shadow-elegant)' }}
            >
              <div className="relative overflow-hidden">
                <img 
                  src={item.image || "/api/placeholder/400/300"} 
                  alt={item.title}
                  className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="absolute top-3 left-3">
                  <Badge className={getCategoryColor(item.category)}>
                    {item.category}
                  </Badge>
                </div>
                <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                    <Eye size={14} />
                  </Button>
                  <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                    <Download size={14} />
                  </Button>
                </div>

                {(isAdmin || isSubAdmin) && (
                  <div className="absolute bottom-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <AdminControls
                      onEdit={() => handleEdit(item)}
                      onDelete={() => handleDelete(item.id)}
                      className="bg-white/20 backdrop-blur-sm rounded-lg p-1"
                    />
                  </div>
                )}
              </div>
              
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2 text-sm text-muted-foreground">
                  <Calendar size={14} />
                  <span>{new Date(item.created_at).toLocaleDateString()}</span>
                </div>
                <h3 className="font-semibold text-lg mb-2 group-hover:text-primary transition-colors line-clamp-1">
                  {item.title}
                </h3>
                <p className="text-sm text-muted-foreground leading-relaxed line-clamp-2">
                  {item.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <div className="text-muted-foreground mb-4">
              <Filter size={48} className="mx-auto mb-4 opacity-50" />
              <p className="text-lg">No items found for the selected category</p>
            </div>
          </div>
        )}

        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-secondary to-muted">
            <Calendar size={20} className="text-primary" />
            <span className="text-sm font-medium">{archiveItems.length} archived items and growing</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Archive;