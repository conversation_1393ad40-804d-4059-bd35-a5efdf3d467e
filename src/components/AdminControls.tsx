import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Plus, Edit, Trash2, Archive } from 'lucide-react';

interface AdminControlsProps {
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onArchive?: () => void;
  showArchive?: boolean;
  className?: string;
}

const AdminControls = ({
  onAdd,
  onEdit,
  onDelete,
  onArchive,
  showArchive = false,
  className = ""
}: AdminControlsProps) => {
  const { isAuthenticated, isAdmin, isSubAdmin } = useAuth();

  if (!isAuthenticated) return null;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {onAdd && (
        <Button
          onClick={onAdd}
          size="sm"
          className="bg-primary hover:bg-primary/90"
        >
          <Plus size={16} />
          <span className="ml-2">Add New</span>
        </Button>
      )}
      
      {onEdit && (
        <Button
          onClick={onEdit}
          variant="outline"
          size="sm"
        >
          <Edit size={16} />
          <span className="ml-2">Edit</span>
        </Button>
      )}
      
      {onDelete && (
        <Button
          onClick={onDelete}
          variant="outline"
          size="sm"
          className="text-destructive hover:text-destructive border-destructive/20 hover:bg-destructive/10"
        >
          <Trash2 size={16} />
          <span className="ml-2">Delete</span>
        </Button>
      )}
      
      {showArchive && onArchive && isAdmin && (
        <Button
          onClick={onArchive}
          variant="outline"
          size="sm"
          className="text-orange-600 hover:text-orange-600 border-orange-200 hover:bg-orange-50 dark:border-orange-800 dark:hover:bg-orange-950"
        >
          <Archive size={16} />
          <span className="ml-2">Archive</span>
        </Button>
      )}
    </div>
  );
};

export default AdminControls;